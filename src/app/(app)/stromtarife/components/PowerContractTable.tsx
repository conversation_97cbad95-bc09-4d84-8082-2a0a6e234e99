"use client";
import React from "react";
import { type PowerContract, type Location } from "@prisma/client";
import Table from "~/utils/table/table";
import { type ColDef, type GridOptions } from "ag-grid-community";
import Link from "next/link";
import { FaEdit, FaEye } from "react-icons/fa";


type PowerContractWithLocation = PowerContract & {
  location: {
    id: string;
    name: string;
    ouId: string;
  };
};

interface Props {
  data: PowerContractWithLocation[];
  contractType: "fixed" | "dynamic";
  emptyMessage?: string;
}

const formatCurrency = (value: number): string => {
  return new Intl.NumberFormat('de-DE', {
    minimumFractionDigits: 4,
    maximumFractionDigits: 4,
  }).format(value);
};

const formatDate = (date: Date): string => {
  return new Intl.DateTimeFormat('de-DE').format(date);
};

export const PowerContractTable = ({ data, contractType, emptyMessage }: Props) => {
  const gridOptions: GridOptions = {
    rowModelType: "clientSide",
    defaultColDef: {
      sortable: true,
      filter: true,
      resizable: true,
      filterParams: {
        buttons: ["reset", "apply"],
      },
    },
    rowHeight: 32,
    pagination: true,
    paginationPageSize: 10,
  };
  const columnDefs: ColDef[] = [
    {
      headerName: "Standort",
      field: "location.name",
      pinned: "left",
      width: 200,
      cellRenderer: (params: any) => {
        return (
          <div className="flex flex-col">
            <span className="font-medium">{params.data.location.name}</span>
            <span className="text-xs text-gray-500">{params.data.location.id}</span>
          </div>
        );
      },
    },
    {
      headerName: "Vertragstyp",
      field: "typeOfContract",
      width: 120,
      cellRenderer: (params: any) => {
        const type = params.value;
        const isFixed = type === 'fixed' || type === 'Contract';
        return (
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
            isFixed 
              ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
              : 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
          }`}>
            {isFixed ? 'Fix' : 'Dynamisch'}
          </span>
        );
      },
    },
    {
      headerName: "Laufzeit",
      field: "start",
      width: 180,
      cellRenderer: (params: any) => {
        const start = new Date(params.data.start);
        const end = new Date(params.data.end);
        const isActive = new Date() >= start && new Date() <= end;
        
        return (
          <div className="flex flex-col">
            <span className="text-sm">
              {formatDate(start)} - {formatDate(end)}
            </span>
            <span className={`text-xs ${
              isActive 
                ? 'text-green-600 dark:text-green-400' 
                : 'text-red-600 dark:text-red-400'
            }`}>
              {isActive ? '● Aktiv' : '● Inaktiv'}
            </span>
          </div>
        );
      },
    },
    {
      headerName: contractType === "fixed" ? "kWh-Preis (Fix)" : "Basis kWh-Preis",
      field: contractType === "fixed" ? "kwhPrice" : "baseKWhPrice",
      width: 140,
      cellRenderer: (params: any) => {
        const price = contractType === "fixed" ? params.data.kwhPrice : params.data.baseKWhPrice;
        if (price === null || price === undefined) {
          return <span className="text-gray-400">-</span>;
        }
        return (
          <div className="text-right">
            <span className="font-medium">{formatCurrency(price)}</span>
            <span className="text-xs text-gray-500 ml-1">€/kWh</span>
          </div>
        );
      },
    },
    {
      headerName: "Monatliche Grundkosten",
      field: "monthlyFixCost",
      width: 160,
      cellRenderer: (params: any) => {
        const cost = params.value;
        if (cost === null || cost === undefined || cost === 0) {
          return <span className="text-gray-400">Keine</span>;
        }
        return (
          <div className="text-right">
            <span className="font-medium">{formatCurrency(cost)}</span>
            <span className="text-xs text-gray-500 ml-1">€/Monat</span>
          </div>
        );
      },
    },
    {
      headerName: "Vertragspartner",
      field: "contractWith",
      width: 150,
      cellRenderer: (params: any) => {
        return params.value || <span className="text-gray-400">-</span>;
      },
    },
    {
      headerName: "Vertragsnummer",
      field: "contractNumber",
      width: 140,
      cellRenderer: (params: any) => {
        return params.value || <span className="text-gray-400">-</span>;
      },
    },
    {
      headerName: "Netzbetreiber",
      field: "supplyNetworkOperator",
      width: 160,
      cellRenderer: (params: any) => {
        return params.value || <span className="text-gray-400">-</span>;
      },
    },
    {
      headerName: "Aktionen",
      field: "actions",
      pinned: "right",
      width: 120,
      cellRenderer: (params: any) => {
        return (
          <div className="flex gap-2">
            <Link href={`/stromtarife/${params.data.id}`}>
              <button className="p-1 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200">
                <FaEdit size={14} />
              </button>
            </Link>
            <Link href={`/location/${params.data.locationId}`}>
              <button className="p-1 text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200">
                <FaEye size={14} />
              </button>
            </Link>
          </div>
        );
      },
    },
  ];

  if (data.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-gray-500 dark:text-gray-400">
        <div className="text-4xl mb-4">
          {contractType === "fixed" ? "🔒" : "📈"}
        </div>
        <p className="text-lg font-medium mb-2">
          {emptyMessage || "Keine Stromtarife vorhanden"}
        </p>
        <p className="text-sm">
          Erstellen Sie einen neuen Stromtarif über den Button oben rechts.
        </p>
      </div>
    );
  }

  return (
    <div style={{ width: "100%", height: "400px" }}>
      <Table
        rowData={data}
        columnDefs={columnDefs}
        gridOptions={gridOptions}
      />
    </div>
  );
};
