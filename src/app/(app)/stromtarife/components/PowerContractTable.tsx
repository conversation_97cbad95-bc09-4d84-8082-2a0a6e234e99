"use client";
import React from "react";
import { type PowerContract, type Location } from "@prisma/client";
import Table from "~/utils/table/table";
import { type ColDef } from "ag-grid-community";
import Link from "next/link";
import { FaEdit } from "react-icons/fa";
import { dateRenderer, twoDecimalPlacesFormatterWithCurrency } from "~/utils/table/formatter";

type PowerContractWithLocation = PowerContract & {
  location: {
    id: string;
    name: string;
    ouId: string;
  };
};

interface Props {
  data: PowerContractWithLocation[];
}

export const PowerContractTable = ({ data }: Props) => {
  const CellRenderer = (params: any) => {
    return (
      <Link href={`/stromtarife/${params?.data?.id}`}>
        <FaEdit className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200" />
      </Link>
    );
  };

  const columnDefs: ColDef[] = [
    {
      field: "location.name",
      headerName: "Standort",
      minWidth: 200,
    },
    {
      field: "typeOfContract",
      headerName: "Vertragstyp",
      cellRenderer: (params: any) => {
        const type = params.value;
        const isFixed = type === 'fixed' || type === 'Contract';
        return isFixed ? 'Fix' : 'Dynamisch';
      },
    },
    {
      field: "start",
      headerName: "Gültig ab",
      cellRenderer: dateRenderer,
    },
    {
      field: "end",
      headerName: "Gültig bis",
      cellRenderer: dateRenderer,
    },
    {
      field: "kwhPrice",
      headerName: "kWh-Preis (Fix)",
      cellRenderer: twoDecimalPlacesFormatterWithCurrency,
    },
    {
      field: "baseKWhPrice",
      headerName: "Basis kWh-Preis",
      cellRenderer: twoDecimalPlacesFormatterWithCurrency,
    },
    {
      field: "monthlyFixCost",
      headerName: "Monatliche Grundkosten",
      cellRenderer: twoDecimalPlacesFormatterWithCurrency,
    },
    {
      field: "contractWith",
      headerName: "Vertragspartner",
    },
    {
      field: "contractNumber",
      headerName: "Vertragsnummer",
    },
    {
      field: "supplyNetworkOperator",
      headerName: "Netzbetreiber",
    },
    {
      field: "action",
      headerName: "Aktion",
      cellRenderer: CellRenderer,
      minWidth: 100,
    },
    {
      field: "id",
      width: 100,
    },
  ];

  return (
    <Table
      gridId="stromtarife"
      columnDefs={columnDefs}
      rowData={data}
    />
  );
};
