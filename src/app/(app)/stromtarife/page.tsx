import React from "react";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import prisma from "~/server/db/prisma";
import Card from "~/component/card";
import Button from "~/component/button";
import Link from "next/link";
import { AiOutlinePlusCircle } from "react-icons/ai";
import { PowerContractTable } from "./components/PowerContractTable";

export const revalidate = 0;

async function getPowerContracts() {
  const session = await getServerSession(authOptions);
  if (!session || session?.user?.role !== Role.ADMIN) {
    return { fixedContracts: [], dynamicContracts: [] };
  }

  const contracts = await prisma.powerContract.findMany({
    include: {
      location: {
        select: {
          id: true,
          name: true,
          ouId: true,
        },
      },
    },
    orderBy: [
      { typeOfContract: 'asc' },
      { start: 'desc' }
    ],
  });

  // Trennung zwischen fixen und dynamischen Verträgen
  const fixedContracts = contracts.filter(contract => 
    contract.typeOfContract === 'fixed' || contract.typeOfContract === 'Contract'
  );
  
  const dynamicContracts = contracts.filter(contract => 
    contract.typeOfContract === 'stock' || contract.typeOfContract === 'Stock'
  );

  return {
    fixedContracts: JSON.parse(JSON.stringify(fixedContracts)),
    dynamicContracts: JSON.parse(JSON.stringify(dynamicContracts)),
  };
}

const StromtarifePage = async () => {
  const session = await getServerSession(authOptions);
  
  if (!session || session?.user?.role !== Role.ADMIN) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-red-500 text-lg">Keine Berechtigung für diese Seite</p>
      </div>
    );
  }

  const { fixedContracts, dynamicContracts } = await getPowerContracts();

  return (
    <>
      <div className="mb-6">
        <h1 className="mb-2 font-bold text-2xl dark:text-white">
          Stromtarife Übersicht
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          Verwaltung aller Stromverträge mit visueller Trennung zwischen fixen und dynamischen Tarifen
        </p>
      </div>

      <div className="mb-3 flex justify-end">
        <Link href="/stromtarife/new">
          <Button type="button" className="bg-green-500 hover:bg-green-600">
            <AiOutlinePlusCircle className="mr-2" size="1.5rem" />
            Neuer Stromtarif
          </Button>
        </Link>
      </div>

      {/* Fixe Stromtarife */}
      <div className="mb-8">
        <Card>
          <div className="mb-6 p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-xl font-semibold text-primary flex items-center">
                  <span className="mr-3 text-2xl">🔒</span>
                  Fixe Stromtarife
                </h2>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  Verträge mit festem kWh-Preis über die gesamte Laufzeit
                </p>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-primary">
                  {fixedContracts.length}
                </div>
                <div className="text-xs text-gray-500">Aktive Verträge</div>
              </div>
            </div>
          </div>
          <div className="p-6">
            <PowerContractTable 
              data={fixedContracts} 
              contractType="fixed"
              emptyMessage="Keine fixen Stromtarife vorhanden"
            />
          </div>
        </Card>
      </div>

      {/* Dynamische Stromtarife */}
      <div className="mb-8">
        <Card>
          <div className="mb-6 p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-xl font-semibold text-primary flex items-center">
                  <span className="mr-3 text-2xl">📈</span>
                  Dynamische Stromtarife
                </h2>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  Verträge mit variablen Preisen basierend auf Marktpreisen (EPEX + Aufschlag)
                </p>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-primary">
                  {dynamicContracts.length}
                </div>
                <div className="text-xs text-gray-500">Aktive Verträge</div>
              </div>
            </div>
          </div>
          <div className="p-6">
            <PowerContractTable 
              data={dynamicContracts} 
              contractType="dynamic"
              emptyMessage="Keine dynamischen Stromtarife vorhanden"
            />
          </div>
        </Card>
      </div>

      {/* Informationsbereich */}
      <Card>
        <div className="p-6">
          <h3 className="text-lg font-semibold mb-4 text-primary">
            ℹ️ Informationen zu Stromtarifen
          </h3>
          <div className="grid md:grid-cols-2 gap-6">
            <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
              <h4 className="font-semibold text-blue-800 dark:text-blue-200 mb-2">
                Fixe Tarife
              </h4>
              <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                <li>• Fester kWh-Preis über die gesamte Vertragslaufzeit</li>
                <li>• Planbare Kosten für Budgetierung</li>
                <li>• Schutz vor Marktpreisschwankungen</li>
                <li>• Monatliche Grundkosten optional</li>
              </ul>
            </div>
            <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
              <h4 className="font-semibold text-green-800 dark:text-green-200 mb-2">
                Dynamische Tarife
              </h4>
              <ul className="text-sm text-green-700 dark:text-green-300 space-y-1">
                <li>• Variable Preise basierend auf EPEX-Spotmarkt</li>
                <li>• Basis-kWh-Preis als Aufschlag zum Marktpreis</li>
                <li>• Potentielle Kosteneinsparungen bei niedrigen Marktpreisen</li>
                <li>• Automatische Preisanpassung</li>
              </ul>
            </div>
          </div>
        </div>
      </Card>
    </>
  );
};

export default StromtarifePage;
