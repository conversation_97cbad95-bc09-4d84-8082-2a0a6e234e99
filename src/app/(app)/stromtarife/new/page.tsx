import React from "react";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import prisma from "~/server/db/prisma";
import { PowerContractForm } from "../components/PowerContractForm";

export const revalidate = 0;

async function getLocations() {
  const session = await getServerSession(authOptions);
  if (!session || session?.user?.role !== Role.ADMIN) {
    return [];
  }

  const locations = await prisma.location.findMany({
    select: {
      id: true,
      name: true,
      ouId: true,
    },
    orderBy: {
      name: 'asc',
    },
  });

  return JSON.parse(JSON.stringify(locations));
}

const NewPowerContractPage = async () => {
  const session = await getServerSession(authOptions);
  
  if (!session || session?.user?.role !== Role.ADMIN) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-red-500 text-lg">Keine <PERSON>rechtigung für diese Seite</p>
      </div>
    );
  }

  const locations = await getLocations();

  return (
    <PowerContractForm locations={locations} />
  );
};

export default NewPowerContractPage;
