import React from "react";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import prisma from "~/server/db/prisma";
import { PowerContractForm } from "../components/PowerContractForm";
import { notFound } from "next/navigation";

export const revalidate = 0;

interface Props {
  params: {
    slug: string;
  };
}

async function getPowerContract(id: string) {
  const session = await getServerSession(authOptions);
  if (!session || session?.user?.role !== Role.ADMIN) {
    return null;
  }

  const contract = await prisma.powerContract.findUnique({
    where: {
      id: id,
    },
    include: {
      location: {
        select: {
          id: true,
          name: true,
          ouId: true,
        },
      },
    },
  });

  return contract ? JSON.parse(JSON.stringify(contract)) : null;
}

async function getLocations() {
  const session = await getServerSession(authOptions);
  if (!session || session?.user?.role !== Role.ADMIN) {
    return [];
  }

  const locations = await prisma.location.findMany({
    select: {
      id: true,
      name: true,
      ouId: true,
    },
    orderBy: {
      name: 'asc',
    },
  });

  return JSON.parse(JSON.stringify(locations));
}

const EditPowerContractPage = async ({ params }: Props) => {
  const session = await getServerSession(authOptions);
  
  if (!session || session?.user?.role !== Role.ADMIN) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-red-500 text-lg">Keine Berechtigung für diese Seite</p>
      </div>
    );
  }

  const contract = await getPowerContract(params.slug);
  
  if (!contract) {
    notFound();
  }

  const locations = await getLocations();

  return (
    <PowerContractForm 
      contract={contract} 
      locations={locations} 
    />
  );
};

export default EditPowerContractPage;
