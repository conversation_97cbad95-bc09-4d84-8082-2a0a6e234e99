import { type NextApiRequest, type NextApiResponse } from "next";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import prisma from "../../../server/db/prisma";
import { getOusBelowOu } from "~/server/model/ou/func";

const upsert = async (req: NextApiRequest, res: NextApiResponse) => {
  // Authentifizierung prüfen
  const session = await getServerSession(req, res, authOptions);
  if (!session || ![Role.ADMIN, Role.CPO, Role.CARD_MANAGER].includes(session?.user?.role)) {
    return res.status(403).json({ error: "Keine Berechtigung" });
  }

  if (req.body) {
    const { data: data } = req.body;

    if (!data.end) {
      data.end = "2100-12-31";
    }

    // Bei Update: Prüfen ob Benutzer Zugriff auf den Vertrag hat
    if (data.id) {
      const existingContract = await prisma.powerContract.findUnique({
        where: { id: data.id },
        include: { location: { select: { ouId: true } } }
      });

      if (!existingContract) {
        return res.status(404).json({ error: "Vertrag nicht gefunden" });
      }

      // Für nicht-ADMIN: Prüfen ob Vertrag zur eigenen OU oder Unter-OU gehört
      if (session.user.role !== Role.ADMIN) {
        const currentOu = session.user.selectedOu || session.user.ou;
        const ousBelow = await getOusBelowOu(currentOu);
        const ouIds = ousBelow.map(ou => ou.id);

        if (!ouIds.includes(existingContract.location.ouId)) {
          return res.status(403).json({ error: "Keine Berechtigung für diesen Vertrag" });
        }
      }

      await prisma.powerContract.update({
        where: {
          id: data.id,
        },
        data: {
          ...data,
          monthlyFixCost: parseFloat(data.monthlyFixCost) || 0,
          baseKWhPrice: parseFloat(data.baseKWhPrice) || 0,
          kwhPrice: parseFloat(data.kwhPrice) || 0,
          start: new Date(data.start),
          end: new Date(data.end),
        },
      });
    } else {
      // Bei Create: Prüfen ob Standort zur eigenen OU oder Unter-OU gehört
      if (session.user.role !== Role.ADMIN) {
        const location = await prisma.location.findUnique({
          where: { id: data.locationId },
          select: { ouId: true }
        });

        if (!location) {
          return res.status(404).json({ error: "Standort nicht gefunden" });
        }

        const currentOu = session.user.selectedOu || session.user.ou;
        const ousBelow = await getOusBelowOu(currentOu);
        const ouIds = ousBelow.map(ou => ou.id);

        if (!ouIds.includes(location.ouId)) {
          return res.status(403).json({ error: "Keine Berechtigung für diesen Standort" });
        }
      }

      await prisma.powerContract.create({
        data: {
          ...data,
          monthlyFixCost: parseFloat(data.monthlyFixCost) || 0,
          baseKWhPrice: parseFloat(data.baseKWhPrice) || 0,
          kwhPrice: parseFloat(data.kwhPrice) || 0,
          start: new Date(data.start),
          end: new Date(data.end),
          locationId: data.locationId,
        },
      });
    }

    res.status(200).json(data);
  } else {
    res.status(500).json("");
  }
};

export default upsert;
